"""
Playwright 快速入门 Demo
这个文件演示了 Playwright 的基本用法，包括：
1. 浏览器启动和页面操作
2. 元素定位和交互
3. 截图功能
4. 表单填写
5. 等待和断言
"""

from playwright.sync_api import sync_playwright
import time


def basic_navigation_demo():
    """基础导航演示 - 打开网页并进行基本操作"""
    print("🚀 开始基础导航演示...")

    with sync_playwright() as p:
        # 启动浏览器（可选择 chromium, firefox, webkit）
        browser = p.chromium.launch(headless=False)  # headless=False 显示浏览器窗口

        # 创建新的浏览器上下文（相当于一个隐私模式会话）
        context = browser.new_context()

        # 创建新页面
        page = context.new_page()

        # 导航到网页
        print("📖 正在访问百度首页...")
        page.goto("https://www.baidu.com")

        # 等待页面加载完成
        page.wait_for_load_state("networkidle")

        # 获取页面标题
        title = page.title()
        print(f"📄 页面标题: {title}")

        # 截图
        page.screenshot(path="screenshots/baidu_homepage.png")
        print("📸 已保存首页截图")

        # 等待2秒让用户看到效果
        time.sleep(2)

        # 关闭浏览器
        browser.close()
        print("✅ 基础导航演示完成\n")


def search_demo():
    """搜索功能演示 - 在百度进行搜索"""
    print("🔍 开始搜索功能演示...")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        # 访问百度
        page.goto("https://www.baidu.com")
        page.wait_for_load_state("networkidle")

        # 定位搜索框并输入关键词
        search_box = page.locator("#kw")  # 使用 ID 选择器
        search_box.fill("Playwright 自动化测试")
        print("⌨️  已输入搜索关键词")

        # 点击搜索按钮
        search_button = page.locator("#su")
        search_button.click()
        print("🔍 已点击搜索按钮")

        # 等待搜索结果加载
        page.wait_for_selector(".result")
        print("📋 搜索结果已加载")

        # 获取第一个搜索结果的标题
        first_result = page.locator(".result").first
        result_title = first_result.locator("h3").text_content()
        print(f"🎯 第一个搜索结果: {result_title}")

        # 截图保存搜索结果
        page.screenshot(path="screenshots/search_results.png")
        print("📸 已保存搜索结果截图")

        time.sleep(3)
        browser.close()
        print("✅ 搜索功能演示完成\n")


def form_interaction_demo():
    """表单交互演示 - 演示各种表单元素的操作"""
    print("📝 开始表单交互演示...")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        # 访问一个包含表单的测试页面
        page.goto("https://httpbin.org/forms/post")
        page.wait_for_load_state("networkidle")

        # 填写文本输入框
        page.fill('input[name="custname"]', "张三")
        print("✏️  已填写客户姓名")

        # 填写邮箱
        page.fill('input[name="custemail"]', "<EMAIL>")
        print("📧 已填写邮箱地址")

        # 选择下拉菜单
        page.select_option('select[name="size"]', "large")
        print("📋 已选择尺寸")

        # 选择单选按钮
        page.check('input[name="topping"][value="bacon"]')
        print("🥓 已选择配菜")

        # 填写文本域
        page.fill('textarea[name="comments"]', "这是一个 Playwright 自动化测试的演示")
        print("💬 已填写评论")

        # 截图保存表单填写状态
        page.screenshot(path="screenshots/form_filled.png")
        print("📸 已保存表单填写截图")

        time.sleep(3)
        browser.close()
        print("✅ 表单交互演示完成\n")


def wait_and_assertion_demo():
    """等待和断言演示 - 演示如何等待元素和进行断言"""
    print("⏳ 开始等待和断言演示...")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()
        page = context.new_page()

        # 访问一个动态加载内容的页面
        page.goto("https://httpbin.org/delay/2")

        # 等待特定元素出现
        print("⏰ 等待页面内容加载...")
        page.wait_for_selector("pre", timeout=10000)  # 等待最多10秒

        # 断言页面标题
        assert "httpbin" in page.title().lower()
        print("✅ 页面标题断言通过")

        # 断言元素是否可见
        content_element = page.locator("pre")
        assert content_element.is_visible()
        print("✅ 内容元素可见性断言通过")

        # 获取并验证页面内容
        content = content_element.text_content()
        assert "origin" in content
        print("✅ 页面内容断言通过")

        print(f"📄 页面响应内容: {content[:100]}...")

        time.sleep(2)
        browser.close()
        print("✅ 等待和断言演示完成\n")


def mobile_simulation_demo():
    """移动设备模拟演示 - 模拟移动设备访问网页"""
    print("📱 开始移动设备模拟演示...")

    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(headless=False)

        # 创建移动设备上下文（模拟 iPhone 12）
        iphone_12 = p.devices["iPhone 12"]
        context = browser.new_context(**iphone_12)

        page = context.new_page()

        # 访问响应式网站
        page.goto("https://www.baidu.com")
        page.wait_for_load_state("networkidle")

        print("📱 正在模拟 iPhone 12 访问百度")

        # 获取视口大小
        viewport = page.viewport_size
        print(f"📐 当前视口大小: {viewport['width']} x {viewport['height']}")

        # 截图保存移动端视图
        page.screenshot(path="screenshots/mobile_view.png")
        print("📸 已保存移动端截图")

        time.sleep(3)
        browser.close()
        print("✅ 移动设备模拟演示完成\n")


def main():
    """主函数 - 运行所有演示"""
    print("🎭 Playwright 快速入门演示开始")
    print("=" * 50)

    # 创建截图目录
    import os
    os.makedirs("screenshots", exist_ok=True)

    try:
        # 运行各个演示
        basic_navigation_demo()
        search_demo()
        form_interaction_demo()
        wait_and_assertion_demo()
        mobile_simulation_demo()

        print("🎉 所有演示完成！")
        print("📁 截图已保存到 screenshots/ 目录")
        print("\n💡 提示:")
        print("- 修改 headless=True 可以在后台运行（不显示浏览器窗口）")
        print("- 可以使用 firefox 或 webkit 替代 chromium")
        print("- 更多功能请查看 Playwright 官方文档")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        print("💡 请确保已安装 Playwright 和浏览器驱动")


if __name__ == "__main__":
    main()